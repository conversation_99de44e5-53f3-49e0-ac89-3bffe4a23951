<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AddInbound, QueryGoodsRequest } from '#/api';

import { computed, reactive, ref } from 'vue';

import { ApiComponent, useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { defaultsDeep } from '@vben/utils';

import { EditOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  message,
  Row,
  Select,
  Textarea,
  TypographyText,
} from 'ant-design-vue';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addInboundApi, editInboundApi, getWareHouseListApi, inboundDetailApi } from '#/api';
import { OrderSelector, ProjectSelector } from '#/components';
import ItemsPick from '#/components/business/ItemsPick.vue';
import SnModal from '#/views/warehouse-manage/composables/sn-modal.vue';

const emit = defineEmits(['register', 'ok']);

// 默认数据
const defaultForm: Partial<AddInbound> = {
  id: undefined,
  createBy: 0,
  createTime: '',
  updateBy: 0,
  updateTime: '',
  version: 0,
  inboundReceiptCode: '',
  receiptDate: '',
  projectId: undefined,
  projectName: undefined,
  projectCode: '',
  warehouseId: undefined,
  warehouseCode: '',
  warehouseName: '',
  customerCompanyCode: '',
  customerCompanyName: '',
  sourceDocumentType: '',
  deliveryReceiptId: undefined,
  deliveryReceiptDisplay: '',
  amountWithTax: 0,
  invoicedAmountWithTax: 0,
  status: '',
  approvalStatus: '',
  invoiceStatus: '',
  remarks: '',
  isSnManaged: 0,
  inboundReceiptSourceRelBOS: [],
  inboundReceiptItemBOs: [],
};

const detailForm = ref<Partial<AddInbound>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  // inboundReceiptCode: [{ required: true, message: '入库单编号', trigger: 'change' }],
  // projectName: [{ required: true, message: '所属项目名称', trigger: 'change' }],
  projectCode: [{ required: true, message: '所属项目编号', trigger: 'change' }],
  // sourceDocumentType: [{ required: true, message: '关联单据类型', trigger: 'change' }],
  // deliveryReceiptId: [{ required: true, message: '关联单据', trigger: 'change' }],
  // receiptDate: [{ required: true, message: '入库日期', trigger: 'change' }],
  // customerCompanyName: [{ required: true, message: '上/下游企业', trigger: 'change' }],
  // warehouseName: [{ required: true, message: '仓库名称', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.value.id ? '编辑入库单' : '新增入库单';
});

const init = async (data: any) => {
  if (data.id) {
    const res = await inboundDetailApi(data.id); // 调用真实 API

    // 深度复制确保响应性
    Object.keys(res).forEach((key) => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换inboundReceiptItemBOs字段
    if (!Array.isArray(res.inboundReceiptItemBOs) || res.inboundReceiptItemBOs === null) {
      detailForm.value.inboundReceiptItemBOs = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.value.inboundReceiptItemBOs = [...res.inboundReceiptItemBOs];
    }

    // 强制刷新表格
    if (gridApiLocation?.grid) {
      gridApiLocation.grid.reloadData(detailForm.value.inboundReceiptItemBOs);
    }
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.value.inboundReceiptItemBOs = defaultForm.inboundReceiptItemBOs
      ? [...defaultForm.inboundReceiptItemBOs]
      : [];
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);
    // 显式类型断言确保类型正确
    const submitData = detailForm as Required<AddInbound>;

    // 根据是否存在id判断是新增还是编辑
    const res = detailForm.value.id ? await editInboundApi(submitData) : await addInboundApi(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

// 在表格配置中添加key属性确保强制刷新
const gridLocation: VxeTableGridOptions = {
  ...DETAIL_GRID_OPTIONS,
  data: detailForm.value.inboundReceiptItemBOs,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '单位',
      minWidth: '150px',
    },
    // { field: 'locationType', title: '已入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, //  后台反
    // { field: 'locationType', title: '未入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, // 源单减去已入库
    {
      field: 'inQuantity',
      title: '已入库数量',
      minWidth: '150px',
    },
    {
      field: 'unInQuantity',
      title: '未入库数量',
      minWidth: '150px',
    },
    {
      field: 'quantity',
      title: '本次入库数量',
      minWidth: '150px',
      editRender: {},
      slots: { default: 'edit_quantity' },
    },
    {
      field: 'sourceDocumentCode',
      title: '源单据编号',
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentName',
      title: '源单据名称',
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '源单据商品行号',
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', editRender: {}, slots: { default: 'edit_remarks' }, minWidth: '200px' },
    {
      field: 'serialNumbers',
      title: 'SN码',
      editRender: {},
      slots: { default: 'edit_serial_numbers' },
      minWidth: '150px',
    },
  ],
};

// 删除行
const removeLocationRow = async (gridApi: any) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!Array.isArray(selectRecords)) {
      message.warning('请选择要删除的数据');
      return;
    }

    if (selectRecords.length > 0) {
      const { deleteInboundReceiptItemBOs } = detailForm;

      // 确保 deleteInboundReceiptItemBOs 是数组
      detailForm.value.deleteInboundReceiptItemBOs = Array.isArray(deleteInboundReceiptItemBOs)
        ? [...deleteInboundReceiptItemBOs, ...selectRecords]
        : [...selectRecords];

      $grid.remove(selectRecords);
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

// 创建响应式的查询参数
const purchaseGoodsParams = reactive<QueryGoodsRequest>({
  projectId: undefined,
  ids: [],
});

const activeProjectInfo = ref<any>({});
const handleChangeProjectName = (_value: number, option: any) => {
  detailForm.value.projectCode = option.projectCode;
  detailForm.value.projectName = option.projectName;
  activeProjectInfo.value = option;
  handleConsigneeCompanyOptions();
};

const handleChangeSourceDocumentId = (value: number, option: any) => {
  detailForm.value.sourceDocumentCode = option.orderCode;
};

const handleWarehouseChange = (warehouseId: number, option: any) => {
  detailForm.value.warehouseName = option.label;
  detailForm.value.warehouseCode = option.warehouseCode;
  detailForm.value.warehouseCompanyName = option.warehouseCompanyName;
};
const executorCompanyOptions = ref([]);
const handleConsigneeCompanyOptions = async () => {
  if (!detailForm.value.projectId) {
    return;
  }
  executorCompanyOptions.value = activeProjectInfo.value.projectPartners.filter(
    (item: any) => item.partnerType === 'SUPPLIER',
  );
};
// 注册表格
const [GridLocation, gridApiLocation] = useVbenVxeGrid({
  gridOptions: gridLocation,
});
const ItemsPickRef = ref();
const pickGoods = async () => {
  if (!detailForm.value.projectId) {
    return message.warning('请先选择项目');
  }
  const newGoods = await ItemsPickRef.value.pick({
    sourceDocumentType: 'PURCHASE_ORDER',
    projectId: detailForm.value.projectId,
    orderId: detailForm.value.sourceDocumentId,
  });
  if (!newGoods || newGoods.length === 0) {
    return;
  }
  const currentGoods = gridApiLocation.grid ? gridApiLocation.grid.getTableData().visibleData : [];

  const goodsToInsert = [];
  for (const good of newGoods) {
    const exists = currentGoods.some(
      (item) =>
        item.sourceDocumentItemNumber === good.sourceDocumentItemNumber &&
        item.sourceDocumentId === good.sourceDocumentId,
    );
    if (!exists) {
      good.shippedQuantity = good.quantity;
      goodsToInsert.push(good);
    }
  }
  if (goodsToInsert.length > 0) {
    await gridApiLocation.grid.insertAt(goodsToInsert, -1);
  } else {
    message.warning('没有可插入的新商品，请勿重复选择');
  }
};
const [Modal, modalApi] = useVbenModal({
  connectedComponent: SnModal,
});
const editSerialNumbers = (row: any) => {
  console.log(row);
  modalApi
    .setState({ title: `编辑商品${row.productName}的SN码` })
    .setData(row)
    .open();
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form ref="formRef" :model="detailForm" :rules="rules" v-bind="FORM_PROP" class="px-8">
      <!-- 基本信息 -->
      <Row class="mt-5">
        <!-- 入库单编号 -->
        <Col v-bind="colSpan">
          <FormItem label="入库单编号" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" placeholder="留空自动生成" />
          </FormItem>
        </Col>
        <!-- 入库日期 -->
        <Col v-bind="colSpan">
          <FormItem label="入库日期" name="receiptDate">
            <DatePicker
              v-model:value="detailForm.receiptDate"
              value-format="YYYY-MM-DD hh:mm:ss"
              class="w-full"
              placeholder="请选择入库日期"
            />
          </FormItem>
        </Col>
        <!-- 仓库名称 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库名称" name="warehouseName">
            <ApiComponent
              v-model="detailForm.warehouseId as unknown as string"
              :component="Select"
              :api="getWareHouseListApi"
              label-field="warehouseName"
              value-field="id"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="handleWarehouseChange"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="仓库企业">
            <Input v-model:value="detailForm.warehouseCompanyName" disabled />
          </FormItem>
        </Col>
        <!-- 所属项目编号 -->
        <Col v-bind="colSpan">
          <FormItem label="项目编号" name="projectCode">
            <Input v-model:value="detailForm.projectCode" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目名称" name="projectId">
            <ProjectSelector v-model="detailForm.projectId" @change="handleChangeProjectName" />
          </FormItem>
        </Col>
        <!-- 上游企业 -->
        <Col v-bind="colSpan">
          <FormItem label="上游企业" name="customerCompanyName">
            <Select v-model:value="detailForm.customerCompanyName" :options="executorCompanyOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="采购订单" name="deliveryReceiptId">
            <OrderSelector
              v-model="detailForm.deliveryReceiptId"
              order-type="purchase"
              :project-id="detailForm.projectId"
              :disabled="!detailForm.projectId"
              @change="handleChangeSourceDocumentId"
            />
          </FormItem>
        </Col>
        <!-- 备注 -->
        <Col :span="24">
          <FormItem label="备注" name="remarks" v-bind="FULL_FORM_ITEM_PROP">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>

      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <div>
        <GridLocation>
          <template #toolbar-tools>
            <Button class="mr-2" type="primary" @click="pickGoods">选择商品</Button>
            <Button class="mr-2" danger @click="() => removeLocationRow(gridApiLocation)">删行</Button>
          </template>

          <template #edit_product_name="{ row }">
            <Input v-model:value="row.productName" placeholder="请输入商品名称" />
          </template>

          <template #edit_product_alias="{ row }">
            <Input v-model:value="row.productAlias" placeholder="请输入商品别名" />
          </template>

          <template #edit_product_code="{ row }">
            <Input v-model:value="row.productCode" placeholder="请输入商品编码" />
          </template>

          <template #edit_specifications="{ row }">
            <Input v-model:value="row.specifications" placeholder="请输入规格型号" />
          </template>

          <template #edit_brand_name="{ row }">
            <Input v-model:value="row.brandName" placeholder="请输入商品品牌" />
          </template>

          <template #edit_origin_name="{ row }">
            <Input v-model:value="row.originName" placeholder="请输入生产厂家" />
          </template>

          <template #edit_measure_unit="{ row }">
            <Input v-model:value="row.measureUnit" placeholder="请输入计量单位" />
          </template>

          <template #edit_quantity="{ row }">
            <Input v-model:value="row.quantity" placeholder="请输入本次入库重量" />
          </template>

          <template #edit_source_document_item_number="{ row }">
            <Input v-model:value="row.sourceDocumentItemNumber" placeholder="请输入订单商品行号" />
          </template>

          <template #edit_source_document_display="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="请输入采购订单编号" />
          </template>

          <template #edit_serial_numbers="{ row }">
            <div class="flex">
              <EditOutlined class="ml-2 cursor-pointer" @click="editSerialNumbers(row)" />
              <TypographyText v-if="row.serialNumbers" copyable>{{ row.serialNumbers }}</TypographyText>
            </div>
          </template>

          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" placeholder="请输入备注" />
          </template>
        </GridLocation>
      </div>
      <BasicCaption content="签收信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="签收时间" name="signDate">
            <DatePicker v-model:value="detailForm.signDate" value-format="YYYY-MM-DD hh:mm:ss" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="签收人" name="signee">
            <Input v-model:value="detailForm.signee" placeholder="请输入签收人" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="签收文件">
            <BaseFilePickList v-model="detailForm.signFileId" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="备注" name="signRemarks" v-bind="FULL_FORM_ITEM_PROP">
            <Textarea v-model:value="detailForm.signRemarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>
    </Form>
    <Modal class="w-[1000px]" />
    <ItemsPick ref="ItemsPickRef" />
  </BasicPopup>
</template>

<style></style>
