<script setup lang="ts">
import { ref } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

import { Alert, Button, Form, FormItem, message, Radio, RadioGroup, Textarea } from 'ant-design-vue';

// 定义 props
interface Props {
  existingSnList?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  existingSnList: () => [],
});

const emit = defineEmits<Emits>();

// 定义 emit
interface Emits {
  (e: 'addSnList', snList: string[]): void;
}

const [BatchInputModal, batchInputModalApi] = useVbenModal({
  onConfirm: async () => {
    const success = processBatchInput();
    if (success) {
      batchInputForm.value.snList = '';
      await batchInputModalApi.close();
    }
  },
});

// 暴露打开模态框的方法
const open = () => {
  batchInputModalApi.open();
};

defineExpose({
  open,
});
// 批量录入相关
const batchInputForm = ref({
  delimiter: 'newline',
  snList: '',
});

// 获取分隔符
const getDelimiter = (type: string): string => {
  switch (type) {
    case 'comma': {
      return ',';
    }
    case 'newline': {
      return '\n';
    }
    case 'semicolon': {
      return ';';
    }
    case 'space': {
      return ' ';
    }
    default: {
      return '\n';
    }
  }
};

// 批量录入处理
const processBatchInput = () => {
  const { delimiter, snList } = batchInputForm.value;

  if (!snList.trim()) {
    message.warning('请输入SN码');
    return false;
  }

  const delimiterChar = getDelimiter(delimiter);
  const snArray = snList
    .split(delimiterChar)
    .map((sn) => sn.trim())
    .filter((sn) => sn.length > 0);

  if (snArray.length === 0) {
    message.warning('没有有效的SN码');
    return false;
  }

  // 检查重复（包括输入内容内部重复和与现有数据重复）
  const existingSnSet = new Set(props.existingSnList);
  const inputSnSet = new Set();
  const duplicateInInput: string[] = [];
  const duplicateWithExisting: string[] = [];
  const validSns: string[] = [];

  snArray.forEach((sn) => {
    if (inputSnSet.has(sn)) {
      duplicateInInput.push(sn);
    } else if (existingSnSet.has(sn)) {
      duplicateWithExisting.push(sn);
    } else {
      inputSnSet.add(sn);
      validSns.push(sn);
    }
  });

  // 通过 emit 发送给父组件
  if (validSns.length > 0) {
    emit('addSnList', validSns);
  }

  // 显示结果消息
  let resultMessage = `成功添加 ${validSns.length} 条SN码`;

  if (duplicateInInput.length > 0) {
    resultMessage += `，输入内容中重复的SN码已自动去除: ${duplicateInInput.join(', ')}`;
  }

  if (duplicateWithExisting.length > 0) {
    resultMessage += `，与现有数据重复的SN码已跳过: ${duplicateWithExisting.join(', ')}`;
  }

  if (validSns.length > 0) {
    message.success(resultMessage);
  } else {
    message.warning('没有新的SN码被添加');
  }

  return validSns.length > 0;
};
const clear = async () => {
  await confirm('确认清空所有数据吗？', '确认清空');
  batchInputForm.value.snList = '';
};
</script>

<template>
  <BatchInputModal title="批量录入" class="w-[600px]">
    <Form>
      <FormItem label="分隔符">
        <RadioGroup v-model:value="batchInputForm.delimiter">
          <Radio value="newline">换行</Radio>
          <Radio value="space">空格</Radio>
          <Radio value="comma">逗号</Radio>
          <Radio value="semicolon">分号</Radio>
        </RadioGroup>
      </FormItem>
      <Alert message="重复的序列号或一次添加超过5000字符SN码值，将会被自动删除" type="warning" class="mb-4" />
      <FormItem :label-col="{ span: 0 }">
        <Textarea v-model:value="batchInputForm.snList" placeholder="请输入SN码" :rows="4" />
      </FormItem>
    </Form>
    <template #center-footer>
      <Button danger @click="clear">清空</Button>
    </template>
  </BatchInputModal>
</template>

<style scoped></style>
